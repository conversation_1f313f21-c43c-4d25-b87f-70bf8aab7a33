import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON><PERSON>ooter,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { AlertTriangle, Loader2 } from 'lucide-react';

interface DeleteAllDemographicDataDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirmDelete: () => Promise<void>;
  totalResponses: number;
  isDeleting?: boolean;
}

const DeleteAllDemographicDataDialog: React.FC<DeleteAllDemographicDataDialogProps> = ({
  open,
  onOpenChange,
  onConfirmDelete,
  totalResponses,
  isDeleting = false,
}) => {
  const [confirmationText, setConfirmationText] = useState('');
  const requiredText = 'DELETE ALL DEMOGRAPHIC DATA';
  const isConfirmationValid = confirmationText === requiredText;

  const handleConfirm = async () => {
    if (isConfirmationValid && !isDeleting) {
      await onConfirmDelete();
      setConfirmationText(''); // Reset confirmation text
    }
  };

  const handleCancel = () => {
    setConfirmationText(''); // Reset confirmation text
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            Delete All Demographic Data
          </DialogTitle>
          <DialogDescription className="space-y-3">
            <div className="bg-red-50 border border-red-200 rounded-md p-3 text-red-800">
              <p className="font-semibold mb-2">⚠️ CRITICAL WARNING</p>
              <p className="text-sm">
                This action will permanently delete ALL student demographic data from the system.
              </p>
            </div>
            
            <div className="space-y-2 text-sm">
              <p><strong>What will be deleted:</strong></p>
              <ul className="list-disc list-inside space-y-1 text-gray-600">
                <li>All {totalResponses} demographic response records</li>
                <li>Student country, gender, age information</li>
                <li>Role type and education level data</li>
                <li>University and workplace information</li>
                <li>All associated demographic analytics</li>
              </ul>
            </div>

            <div className="bg-amber-50 border border-amber-200 rounded-md p-3 text-amber-800">
              <p className="font-semibold text-sm">
                ⚠️ This action CANNOT be undone. All demographic data will be permanently lost.
              </p>
            </div>
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div>
            <Label htmlFor="confirmation" className="text-sm font-medium">
              Type <code className="bg-gray-100 px-1 rounded text-xs">{requiredText}</code> to confirm:
            </Label>
            <Input
              id="confirmation"
              value={confirmationText}
              onChange={(e) => setConfirmationText(e.target.value)}
              placeholder="Type the confirmation text..."
              className="mt-1"
              disabled={isDeleting}
            />
          </div>
        </div>

        <DialogFooter className="gap-2">
          <Button 
            variant="outline" 
            onClick={handleCancel}
            disabled={isDeleting}
          >
            Cancel
          </Button>
          <Button 
            variant="destructive" 
            onClick={handleConfirm}
            disabled={!isConfirmationValid || isDeleting}
            className="min-w-[120px]"
          >
            {isDeleting ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Deleting...
              </>
            ) : (
              'Confirm Delete'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default DeleteAllDemographicDataDialog;
