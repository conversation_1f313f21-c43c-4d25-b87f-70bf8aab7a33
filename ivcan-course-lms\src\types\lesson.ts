export interface Lesson {
  id: string;
  title: string;
  slug: string;
  description?: string;
  content: string;
  type: 'content' | 'quiz' | 'video' | 'external';
  module_id: string;
  module_title?: string;
  lesson_number: number;
  created_at: string;
  updated_at: string;
  completed?: boolean;
  previous_lesson_slug?: string;
  next_lesson_slug?: string;
  image_url?: string;
  video_url?: string;
  external_url?: string;
  duration_minutes?: number;
  is_locked?: boolean;
}