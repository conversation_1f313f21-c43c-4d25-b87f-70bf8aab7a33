import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Users, Globe, UserCheck, Briefcase, Loader2, Trash2, AlertTriangle } from 'lucide-react';
import { getDemographicAnalytics, getAllDemographicResponses, deleteAllDemographicData } from '@/services/demographicApi';
import { DemographicAnalytics as DemographicAnalyticsType } from '@/types/demographic';
import { useToast } from '@/hooks/use-toast';
import { useUserRole } from '@/hooks/useUserRole';
import DeleteAllDemographicDataDialog from './DeleteAllDemographicDataDialog';

export function DemographicAnalytics() {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const { toast } = useToast();
  const { isTeacher } = useUserRole();
  const queryClient = useQueryClient();

  // Fetch demographic analytics
  const { data: analytics, isLoading: analyticsLoading, error: analyticsError } = useQuery({
    queryKey: ['demographic-analytics'],
    queryFn: getDemographicAnalytics,
  });

  // Fetch all responses for detailed view
  const { data: responses, isLoading: responsesLoading } = useQuery({
    queryKey: ['demographic-responses'],
    queryFn: getAllDemographicResponses,
  });

  // Mutation for deleting all demographic data
  const deleteAllMutation = useMutation({
    mutationFn: deleteAllDemographicData,
    onSuccess: (result) => {
      toast({
        title: "Success",
        description: `Successfully deleted ${result.deletedCount} demographic response records.`,
        variant: "default",
      });

      // Invalidate and refetch queries to update the UI
      queryClient.invalidateQueries({ queryKey: ['demographic-analytics'] });
      queryClient.invalidateQueries({ queryKey: ['demographic-responses'] });

      setDeleteDialogOpen(false);
    },
    onError: (error: any) => {
      console.error('Error deleting demographic data:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to delete demographic data. Please try again.",
        variant: "destructive",
      });
    },
  });

  const handleDeleteAll = async () => {
    if (!isTeacher) {
      toast({
        title: "Access Denied",
        description: "You don't have permission to perform this action.",
        variant: "destructive",
      });
      return;
    }

    try {
      await deleteAllMutation.mutateAsync();
    } catch (error) {
      // Error is already handled in onError callback
    }
  };

  if (analyticsLoading || responsesLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">Loading demographic analytics...</p>
        </div>
      </div>
    );
  }

  if (analyticsError) {
    return (
      <div className="text-center p-8">
        <p className="text-red-500">Error loading demographic analytics</p>
      </div>
    );
  }

  const renderBreakdownCard = (
    title: string,
    icon: React.ReactNode,
    data: Record<string, number>,
    total: number
  ) => (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {icon}
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          {Object.entries(data)
            .sort(([, a], [, b]) => b - a)
            .map(([key, value]) => {
              const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : '0';
              return (
                <div key={key} className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground truncate flex-1 mr-2">
                    {key}
                  </span>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="text-xs">
                      {value}
                    </Badge>
                    <span className="text-xs text-muted-foreground min-w-[3rem] text-right">
                      {percentage}%
                    </span>
                  </div>
                </div>
              );
            })}
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold mb-2">Demographic Analytics</h2>
          <p className="text-muted-foreground">
            Overview of student demographic information collected through the onboarding questionnaire.
          </p>
        </div>

        {/* Delete All Button - Only show if there are responses and user is teacher */}
        {isTeacher && analytics && analytics.total_responses > 0 && (
          <div className="flex flex-col items-end gap-2">
            <Button
              variant="destructive"
              size="sm"
              onClick={() => setDeleteDialogOpen(true)}
              className="flex items-center gap-2"
              disabled={deleteAllMutation.isPending}
            >
              <Trash2 className="h-4 w-4" />
              Delete All Data
            </Button>
            <div className="flex items-center gap-1 text-xs text-muted-foreground">
              <AlertTriangle className="h-3 w-3" />
              <span>Destructive action</span>
            </div>
          </div>
        )}
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Responses</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics?.total_responses || 0}</div>
            <p className="text-xs text-muted-foreground">
              Students completed questionnaire
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completion Rate</CardTitle>
            <UserCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics?.completion_rate || 0}%</div>
            <p className="text-xs text-muted-foreground">
              Of all registered users
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Countries Represented</CardTitle>
            <Globe className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {analytics?.response_breakdown?.by_country ? 
                Object.keys(analytics.response_breakdown.by_country).length : 0}
            </div>
            <p className="text-xs text-muted-foreground">
              Different countries
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Breakdown Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {analytics?.response_breakdown?.by_country && 
          renderBreakdownCard(
            'By Country',
            <Globe className="h-4 w-4 text-muted-foreground" />,
            analytics.response_breakdown.by_country,
            analytics.total_responses
          )
        }

        {analytics?.response_breakdown?.by_gender && 
          renderBreakdownCard(
            'By Gender',
            <Users className="h-4 w-4 text-muted-foreground" />,
            analytics.response_breakdown.by_gender,
            analytics.total_responses
          )
        }

        {analytics?.response_breakdown?.by_role && 
          renderBreakdownCard(
            'By Role Type',
            <Briefcase className="h-4 w-4 text-muted-foreground" />,
            analytics.response_breakdown.by_role,
            analytics.total_responses
          )
        }
      </div>

      {/* Recent Responses */}
      {responses && responses.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Recent Responses</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {responses.slice(0, 10).map((response) => (
                <div key={response.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="space-y-1">
                    <div className="font-medium">
                      {(response as any).profiles?.first_name && (response as any).profiles?.last_name
                        ? `${(response as any).profiles.first_name} ${(response as any).profiles.last_name}`
                        : 'Anonymous User'
                      }
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {response.responses.country && `From ${response.responses.country}`}
                      {response.responses.role_type && ` • ${response.responses.role_type}`}
                    </div>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {new Date(response.completed_at).toLocaleDateString()}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {(!responses || responses.length === 0) && (
        <Card>
          <CardContent className="text-center py-8">
            <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">No Responses Yet</h3>
            <p className="text-muted-foreground">
              Demographic data will appear here once students complete the onboarding questionnaire.
            </p>
          </CardContent>
        </Card>
      )}

      {/* Delete All Demographic Data Dialog */}
      <DeleteAllDemographicDataDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onConfirmDelete={handleDeleteAll}
        totalResponses={analytics?.total_responses || 0}
        isDeleting={deleteAllMutation.isPending}
      />
    </div>
  );
}
