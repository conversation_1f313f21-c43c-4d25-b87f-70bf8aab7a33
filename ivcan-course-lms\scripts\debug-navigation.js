import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.VITE_SUPABASE_URL, process.env.VITE_SUPABASE_ANON_KEY);

async function checkLessonData() {
  console.log('🔍 Checking lesson data structure...');
  
  // Get a sample of lessons with their module info
  const { data: lessons, error } = await supabase
    .from('lessons')
    .select('id, slug, title, lesson_number, module_id, created_at, modules:module_id(title, module_number)')
    .order('created_at')
    .limit(10);
  
  if (error) {
    console.error('Error:', error);
    return;
  }
  
  console.log('Sample lessons:');
  lessons.forEach(lesson => {
    console.log(`- ${lesson.title}`);
    console.log(`  Slug: ${lesson.slug}`);
    console.log(`  Lesson Number: ${lesson.lesson_number}`);
    console.log(`  Module: ${lesson.modules?.title} (Module #${lesson.modules?.module_number})`);
    console.log(`  Created: ${lesson.created_at}`);
    console.log('');
  });
  
  // Test navigation for a specific lesson
  console.log('🧪 Testing navigation for first lesson...');
  if (lessons.length > 0) {
    const testLesson = lessons[0];
    console.log(`Testing with: ${testLesson.title} (${testLesson.slug})`);
    
    // Test the unified navigation function
    const { data: navResult, error: navError } = await supabase
      .rpc('get_lesson_navigation_enhanced', { p_current_lesson_slug: testLesson.slug });
    
    if (navError) {
      console.error('Navigation error:', navError);
    } else {
      console.log('Navigation result:', JSON.stringify(navResult, null, 2));
    }
  }
  
  // Check for lesson ordering issues within modules
  console.log('🔍 Checking lesson ordering within modules...');
  const { data: modules, error: moduleError } = await supabase
    .from('modules')
    .select('id, title, module_number')
    .order('module_number');
  
  if (moduleError) {
    console.error('Module error:', moduleError);
    return;
  }
  
  for (const module of modules) {
    console.log(`\nModule: ${module.title} (Module #${module.module_number})`);
    
    const { data: moduleLessons, error: lessonError } = await supabase
      .from('lessons')
      .select('slug, title, lesson_number')
      .eq('module_id', module.id)
      .order('lesson_number');
    
    if (lessonError) {
      console.error(`Error fetching lessons for module ${module.title}:`, lessonError);
      continue;
    }
    
    if (moduleLessons && moduleLessons.length > 0) {
      moduleLessons.forEach(lesson => {
        console.log(`  ${lesson.lesson_number}. ${lesson.title} (${lesson.slug})`);
      });
    } else {
      console.log('  No lessons found');
    }
  }
}

checkLessonData().catch(console.error);
