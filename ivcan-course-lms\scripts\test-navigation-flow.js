import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.VITE_SUPABASE_URL, process.env.VITE_SUPABASE_ANON_KEY);

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

async function testNavigationFlow() {
  console.log(`${colors.blue}🚀 Testing Complete Navigation Flow${colors.reset}\n`);
  
  try {
    // Get the first lesson from the first module
    const { data: firstModule, error: moduleError } = await supabase
      .from('modules')
      .select('id, title, module_number')
      .order('module_number')
      .limit(1)
      .single();
    
    if (moduleError || !firstModule) {
      console.error(`${colors.red}❌ Could not find first module:${colors.reset}`, moduleError);
      return;
    }
    
    console.log(`${colors.cyan}Starting with module: ${firstModule.title}${colors.reset}`);
    
    // Get the first lesson in this module
    const { data: firstLesson, error: lessonError } = await supabase
      .from('lessons')
      .select('slug, title, lesson_number')
      .eq('module_id', firstModule.id)
      .order('lesson_number')
      .limit(1)
      .single();
    
    if (lessonError || !firstLesson) {
      console.error(`${colors.red}❌ Could not find first lesson:${colors.reset}`, lessonError);
      return;
    }
    
    console.log(`${colors.cyan}Starting with lesson: ${firstLesson.title} (${firstLesson.slug})${colors.reset}\n`);
    
    // Test navigation flow
    let currentLessonSlug = firstLesson.slug;
    let stepCount = 0;
    const maxSteps = 20; // Prevent infinite loops
    
    while (currentLessonSlug && stepCount < maxSteps) {
      stepCount++;
      console.log(`${colors.blue}Step ${stepCount}: Testing navigation from ${currentLessonSlug}${colors.reset}`);
      
      // Test the unified navigation function
      const { data: navResult, error: navError } = await supabase
        .rpc('get_lesson_navigation_enhanced', { p_current_lesson_slug: currentLessonSlug });
      
      if (navError) {
        console.error(`${colors.red}❌ Navigation error:${colors.reset}`, navError);
        break;
      }
      
      if (!navResult) {
        console.error(`${colors.red}❌ No navigation result returned${colors.reset}`);
        break;
      }
      
      console.log(`${colors.green}✅ Current: ${navResult.current_lesson?.slug}${colors.reset}`);
      console.log(`${colors.cyan}   Previous: ${navResult.previous_slug || 'None'}${colors.reset}`);
      console.log(`${colors.cyan}   Next: ${navResult.next_slug || 'None'}${colors.reset}`);
      console.log(`${colors.cyan}   Is Last: ${navResult.is_last_lesson}${colors.reset}`);
      
      if (navResult.is_last_lesson) {
        console.log(`${colors.green}🎉 Reached the last lesson successfully!${colors.reset}`);
        break;
      }
      
      if (!navResult.next_slug) {
        console.log(`${colors.yellow}⚠️ No next lesson found but not marked as last lesson${colors.reset}`);
        break;
      }
      
      // Move to next lesson
      currentLessonSlug = navResult.next_slug;
      console.log('');
    }
    
    if (stepCount >= maxSteps) {
      console.log(`${colors.yellow}⚠️ Stopped after ${maxSteps} steps to prevent infinite loop${colors.reset}`);
    }
    
    console.log(`\n${colors.blue}📊 Navigation Flow Summary:${colors.reset}`);
    console.log(`${colors.cyan}Total steps tested: ${stepCount}${colors.reset}`);
    console.log(`${colors.cyan}Final lesson: ${currentLessonSlug}${colors.reset}`);
    
  } catch (error) {
    console.error(`${colors.red}❌ Test failed:${colors.reset}`, error);
  }
}

async function testSpecificNavigation() {
  console.log(`\n${colors.blue}🧪 Testing Specific Navigation Cases${colors.reset}\n`);
  
  // Test cases for different scenarios
  const testCases = [
    'patient-history-and-explanation-of-procedure', // First lesson in module 2
    'seeking-consent-during-iv-cannulation', // Second lesson in module 2
    'medico-legal-issues-in-iv-cannulation-and-contrast-administration', // Last lesson in module 2
    'constrast-media-and-its-pharmacology', // First lesson in module 3
    'additional-precautions-for-iv-cannulation' // Last lesson in course
  ];
  
  for (const lessonSlug of testCases) {
    console.log(`${colors.cyan}Testing: ${lessonSlug}${colors.reset}`);
    
    try {
      // Test both the database function and the API function
      const { data: dbResult, error: dbError } = await supabase
        .rpc('get_lesson_navigation_enhanced', { p_current_lesson_slug: lessonSlug });
      
      if (dbError) {
        console.error(`${colors.red}❌ DB Function Error:${colors.reset}`, dbError.message);
        continue;
      }
      
      console.log(`${colors.green}✅ DB Function Result:${colors.reset}`);
      console.log(`   Next: ${dbResult.next_slug || 'None'}`);
      console.log(`   Is Last: ${dbResult.is_last_lesson}`);
      
      // Test the JavaScript function (simulate what the frontend does)
      const { data: currentLesson, error: lessonError } = await supabase
        .from('lessons')
        .select('id, slug, module_id, lesson_number, created_at')
        .eq('slug', lessonSlug)
        .single();
      
      if (lessonError) {
        console.error(`${colors.red}❌ Could not fetch lesson:${colors.reset}`, lessonError.message);
        continue;
      }
      
      // Test finding next lesson in same module
      const { data: nextLessons, error: nextError } = await supabase
        .from('lessons')
        .select('slug, lesson_number, title')
        .eq('module_id', currentLesson.module_id)
        .gt('lesson_number', currentLesson.lesson_number)
        .order('lesson_number', { ascending: true })
        .limit(1);
      
      if (nextError) {
        console.error(`${colors.red}❌ Error finding next lesson:${colors.reset}`, nextError.message);
      } else {
        console.log(`${colors.green}✅ JS Logic Result:${colors.reset}`);
        if (nextLessons && nextLessons.length > 0) {
          console.log(`   Next: ${nextLessons[0].slug}`);
        } else {
          console.log(`   Next: None (end of module)`);
        }
      }
      
    } catch (error) {
      console.error(`${colors.red}❌ Test error for ${lessonSlug}:${colors.reset}`, error.message);
    }
    
    console.log('');
  }
}

async function main() {
  await testNavigationFlow();
  await testSpecificNavigation();
  
  console.log(`\n${colors.green}🎯 Navigation testing complete!${colors.reset}`);
  console.log(`\n${colors.cyan}If you see any issues above, they indicate problems with the navigation logic.${colors.reset}`);
  console.log(`${colors.cyan}If all tests pass, the issue might be in the frontend component integration.${colors.reset}`);
}

main().catch(console.error);
